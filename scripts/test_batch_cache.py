#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量缓存系统测试脚本

测试新的批量导向缓存管理器的功能：
- 批量缓存操作
- 部分命中处理
- 文件持久化
- 向后兼容性

作者: Assistant
日期: 2024
"""

import tempfile
import shutil
from pathlib import Path
from cache_manager import BatchCacheManager

def test_batch_cache_operations():
    """测试批量缓存操作"""
    print("🧪 测试批量缓存操作...")
    
    # 创建临时缓存目录
    with tempfile.TemporaryDirectory() as temp_dir:
        cache_dir = Path(temp_dir)
        cache_manager = BatchCacheManager(
            cache_dir=cache_dir,
            memory_ttl=3600,
            file_ttl=86400,
            enable_compression=True
        )
        
        # 测试数据
        smiles_list = ["CCO", "CCC", "CCCC", "CCCCC"]
        results = {
            "CCO": {"property1": 1.0, "property2": "value1"},
            "CCC": {"property1": 2.0, "property2": "value2"},
            "CCCC": {"property1": 3.0, "property2": "value3"},
            "CCCCC": {"property1": 4.0, "property2": "value4"}
        }
        
        # 生成批量键
        batch_key = cache_manager._generate_batch_key(smiles_list)
        print(f"  批量键: {batch_key}")
        
        # 缓存批量数据
        success = cache_manager.cache_batch(batch_key, smiles_list, results, 'brain_api')
        assert success, "批量缓存失败"
        print("  ✅ 批量缓存成功")
        
        # 测试完全命中
        cached_results, missing_smiles = cache_manager.get_cached_batch(batch_key, smiles_list, 'brain_api')
        assert len(cached_results) == len(smiles_list), f"完全命中失败: {len(cached_results)} != {len(smiles_list)}"
        assert len(missing_smiles) == 0, f"不应有缺失SMILES: {missing_smiles}"
        print("  ✅ 完全命中测试通过")
        
        # 测试部分命中
        partial_smiles = ["CCO", "CCC", "NEW_SMILES"]
        cached_results, missing_smiles = cache_manager.get_cached_batch(batch_key, partial_smiles, 'brain_api')
        assert len(cached_results) == 2, f"部分命中失败: {len(cached_results)} != 2"
        assert "NEW_SMILES" in missing_smiles, "缺失SMILES检测失败"
        print("  ✅ 部分命中测试通过")
        
        # 测试统计信息
        stats = cache_manager.get_stats()
        print(f"  📊 缓存统计: 命中={stats.hits}, 部分命中={stats.partial_hits}, 缺失={stats.misses}")
        
        return True

def test_individual_cache_compatibility():
    """测试个体缓存向后兼容性"""
    print("🧪 测试个体缓存向后兼容性...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        cache_dir = Path(temp_dir)
        cache_manager = BatchCacheManager(cache_dir=cache_dir)
        
        # 测试个体缓存操作
        key = "brain_api:CCO"
        data = {"property": "test_value"}
        
        # 设置个体缓存
        success = cache_manager.set(key, data)
        assert success, "个体缓存设置失败"
        print("  ✅ 个体缓存设置成功")
        
        # 获取个体缓存
        cached_data = cache_manager.get(key)
        assert cached_data == data, f"个体缓存获取失败: {cached_data} != {data}"
        print("  ✅ 个体缓存获取成功")
        
        # 删除个体缓存
        deleted = cache_manager.delete(key)
        assert deleted, "个体缓存删除失败"
        print("  ✅ 个体缓存删除成功")
        
        # 验证删除
        cached_data = cache_manager.get(key)
        assert cached_data is None, "个体缓存删除验证失败"
        print("  ✅ 个体缓存删除验证通过")
        
        return True

def test_file_persistence():
    """测试文件持久化"""
    print("🧪 测试文件持久化...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        cache_dir = Path(temp_dir)
        
        # 第一个缓存管理器实例
        cache_manager1 = BatchCacheManager(cache_dir=cache_dir)
        
        smiles_list = ["CCO", "CCC"]
        results = {
            "CCO": {"property": 1.0},
            "CCC": {"property": 2.0}
        }
        batch_key = cache_manager1._generate_batch_key(smiles_list)
        
        # 缓存数据
        cache_manager1.cache_batch(batch_key, smiles_list, results, 'brain_api')
        print("  ✅ 数据已缓存到文件")
        
        # 创建新的缓存管理器实例（模拟重启）
        cache_manager2 = BatchCacheManager(cache_dir=cache_dir)
        
        # 从文件加载数据
        cached_results, missing_smiles = cache_manager2.get_cached_batch(batch_key, smiles_list, 'brain_api')
        assert len(cached_results) == len(smiles_list), "文件持久化失败"
        assert len(missing_smiles) == 0, "文件持久化验证失败"
        print("  ✅ 文件持久化验证通过")
        
        return True

def test_missing_smiles_detection():
    """测试缺失SMILES检测"""
    print("🧪 测试缺失SMILES检测...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        cache_dir = Path(temp_dir)
        cache_manager = BatchCacheManager(cache_dir=cache_dir)
        
        # 缓存部分SMILES
        cache_manager.set("brain_api:CCO", {"property": 1.0})
        cache_manager.set("brain_api:CCC", {"property": 2.0})
        
        # 测试缺失SMILES检测
        test_smiles = ["CCO", "CCC", "CCCC", "CCCCC"]
        missing_smiles = cache_manager.get_missing_smiles(test_smiles, 'brain_api')
        
        expected_missing = ["CCCC", "CCCCC"]
        assert set(missing_smiles) == set(expected_missing), f"缺失SMILES检测失败: {missing_smiles} != {expected_missing}"
        print("  ✅ 缺失SMILES检测通过")
        
        return True

def test_cache_cleanup():
    """测试缓存清理"""
    print("🧪 测试缓存清理...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        cache_dir = Path(temp_dir)
        cache_manager = BatchCacheManager(cache_dir=cache_dir)
        
        # 添加一些缓存数据
        cache_manager.set("brain_api:CCO", {"property": 1.0})
        cache_manager.cache_batch("test_batch", ["CCC"], {"CCC": {"property": 2.0}}, 'brain_api')
        
        # 验证数据存在
        assert cache_manager.get("brain_api:CCO") is not None, "缓存数据不存在"
        
        # 清理缓存
        cache_manager.clear()
        
        # 验证数据已清理
        assert cache_manager.get("brain_api:CCO") is None, "缓存清理失败"
        
        stats = cache_manager.get_stats()
        assert stats.memory_entries == 0, "内存缓存清理失败"
        assert stats.batch_entries == 0, "批量缓存清理失败"
        
        print("  ✅ 缓存清理验证通过")
        
        return True

def main():
    """运行所有测试"""
    print("🚀 开始批量缓存系统测试")
    print("=" * 50)
    
    tests = [
        test_batch_cache_operations,
        test_individual_cache_compatibility,
        test_file_persistence,
        test_missing_smiles_detection,
        test_cache_cleanup
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
                print("✅ 测试通过\n")
            else:
                failed += 1
                print("❌ 测试失败\n")
        except Exception as e:
            failed += 1
            print(f"❌ 测试异常: {e}\n")
    
    print("=" * 50)
    print(f"📊 测试结果: {passed} 通过, {failed} 失败")
    
    if failed == 0:
        print("🎉 所有测试通过！批量缓存系统工作正常。")
        return True
    else:
        print("⚠️ 部分测试失败，请检查实现。")
        return False

if __name__ == "__main__":
    main()
