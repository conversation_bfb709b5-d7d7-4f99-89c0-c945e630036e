#!/usr/bin/env python3
"""
Debug script to identify the cache file path bug
"""

import os
from pathlib import Path
from cache_manager import <PERSON>ch<PERSON><PERSON><PERSON><PERSON><PERSON>

def debug_cache_paths():
    """Debug cache file paths"""
    print("🔍 Debugging cache file paths...")

    # Test with different cache directories
    test_dirs = [
        "./test_cache",
        "../cache",
        "cache"
    ]

    for cache_dir in test_dirs:
        print(f"\n📁 Testing cache_dir: {cache_dir}")
        print(f"   Absolute path: {Path(cache_dir).resolve()}")

        try:
            cache_manager = BatchCacheManager(
                cache_dir=Path(cache_dir),
                enable_file_cache=True
            )

            print(f"   Brain API cache file: {cache_manager.brain_api_cache_file}")
            print(f"   ClickHouse cache file: {cache_manager.clickhouse_cache_file}")
            print(f"   Brain API exists: {cache_manager.brain_api_cache_file.exists()}")
            print(f"   ClickHouse exists: {cache_manager.clickhouse_cache_file.exists()}")

            # Test cache operations
            cache_manager.set("brain_api:test", {"test": "data"})
            cache_manager.set("clickhouse:test", {"test": "data"})

            print(f"   After setting data:")
            print(f"   Brain API exists: {cache_manager.brain_api_cache_file.exists()}")
            print(f"   ClickHouse exists: {cache_manager.clickhouse_cache_file.exists()}")

            # Check what files were actually created
            cache_path = Path(cache_dir)
            if cache_path.exists():
                print(f"   Files in cache directory:")
                for file in cache_path.rglob("*"):
                    if file.is_file():
                        print(f"     {file}")

            # Clean up
            cache_manager.clear()

        except Exception as e:
            print(f"   Error: {e}")

def test_specific_bug():
    """Test the specific bug mentioned: cache/brain_api/clickhouse_cache.pkl"""
    print("\n🐛 Testing specific bug scenario...")

    # Create cache manager with cache/brain_api as cache_dir
    cache_dir = Path("cache/brain_api")
    print(f"📁 Cache dir: {cache_dir}")
    print(f"   Absolute path: {cache_dir.resolve()}")

    try:
        cache_manager = BatchCacheManager(
            cache_dir=cache_dir,
            enable_file_cache=True
        )

        print(f"   Brain API cache file: {cache_manager.brain_api_cache_file}")
        print(f"   ClickHouse cache file: {cache_manager.clickhouse_cache_file}")

        # Test setting ClickHouse data
        cache_manager.set("clickhouse:test", {"test": "clickhouse_data"})

        print(f"   After setting ClickHouse data:")
        print(f"   Brain API exists: {cache_manager.brain_api_cache_file.exists()}")
        print(f"   ClickHouse exists: {cache_manager.clickhouse_cache_file.exists()}")

        # Check what files were actually created
        if cache_dir.exists():
            print(f"   Files in cache directory:")
            for file in cache_dir.rglob("*"):
                if file.is_file():
                    print(f"     {file}")

        # Check parent directory too
        parent_dir = cache_dir.parent
        print(f"   Files in parent directory ({parent_dir}):")
        for file in parent_dir.rglob("*.pkl"):
            print(f"     {file}")

        # Clean up
        cache_manager.clear()

    except Exception as e:
        print(f"   Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_cache_paths()
    test_specific_bug()
