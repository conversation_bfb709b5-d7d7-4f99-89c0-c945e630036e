#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量导向缓存管理器模块

提供批量级别的缓存功能，支持内存缓存和持久化文件缓存，
包含TTL机制、压缩存储和缓存统计功能。

新特性：
- 批量级别缓存操作（整个API响应批次作为单个单元）
- 简化的两文件结构（brain_api_cache.pkl, clickhouse_cache.pkl）
- 部分批次命中支持（缓存的SMILES与新API调用结合）
- 基于SMILES列表排序哈希的批次键策略

作者: Assistant
日期: 2024
"""

import pickle
import gzip
import time
import logging
import hashlib
import fcntl
from pathlib import Path
from typing import Any, Dict, Optional, Set, Tuple, List
from dataclasses import dataclass, field
from threading import RLock
from contextlib import contextmanager


@dataclass
class CacheEntry:
    """缓存条目"""
    data: Any
    timestamp: float
    access_count: int = 0
    last_access: float = field(default_factory=time.time)

    def is_expired(self, ttl: float) -> bool:
        """检查是否过期"""
        return time.time() - self.timestamp > ttl

    def touch(self):
        """更新访问时间和计数"""
        self.last_access = time.time()
        self.access_count += 1


@dataclass
class BatchCacheEntry:
    """批量缓存条目"""
    smiles_list: List[str]  # 批次中的SMILES列表
    results: Dict[str, Any]  # SMILES -> 结果的映射
    timestamp: float
    access_count: int = 0
    last_access: float = field(default_factory=time.time)

    def is_expired(self, ttl: float) -> bool:
        """检查是否过期"""
        return time.time() - self.timestamp > ttl

    def touch(self):
        """更新访问时间和计数"""
        self.last_access = time.time()
        self.access_count += 1

    def get_results_for_smiles(self, smiles_subset: List[str]) -> Dict[str, Any]:
        """获取指定SMILES子集的结果"""
        return {smiles: self.results[smiles] for smiles in smiles_subset if smiles in self.results}


@dataclass
class ConsolidatedCache:
    """合并缓存文件结构"""
    # 个体SMILES缓存（向后兼容）
    individual_entries: Dict[str, CacheEntry] = field(default_factory=dict)
    # 批量缓存条目
    batch_entries: Dict[str, BatchCacheEntry] = field(default_factory=dict)
    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)
    created_at: float = field(default_factory=time.time)
    last_modified: float = field(default_factory=time.time)

    def cleanup_expired(self, ttl: float) -> int:
        """清理过期条目，返回清理数量"""
        expired_count = 0

        # 清理个体条目
        expired_keys = [
            key for key, entry in self.individual_entries.items()
            if entry.is_expired(ttl)
        ]
        for key in expired_keys:
            del self.individual_entries[key]
            expired_count += 1

        # 清理批量条目
        expired_batch_keys = [
            key for key, entry in self.batch_entries.items()
            if entry.is_expired(ttl)
        ]
        for key in expired_batch_keys:
            del self.batch_entries[key]
            expired_count += 1

        if expired_count > 0:
            self.last_modified = time.time()

        return expired_count


@dataclass
class CacheStats:
    """缓存统计信息"""
    hits: int = 0
    misses: int = 0
    partial_hits: int = 0  # 部分批次命中
    evictions: int = 0
    memory_entries: int = 0
    file_entries: int = 0
    batch_entries: int = 0  # 批量条目数
    total_size_bytes: int = 0

    @property
    def hit_rate(self) -> float:
        """命中率"""
        total = self.hits + self.misses + self.partial_hits
        return (self.hits + self.partial_hits) / total if total > 0 else 0.0

    @property
    def full_hit_rate(self) -> float:
        """完全命中率"""
        total = self.hits + self.misses + self.partial_hits
        return self.hits / total if total > 0 else 0.0

    def reset(self):
        """重置统计"""
        self.hits = 0
        self.misses = 0
        self.partial_hits = 0
        self.evictions = 0


class BatchCacheManager:
    """批量导向缓存管理器 - 简化的两文件结构"""

    def __init__(
        self,
        cache_dir: Path,
        memory_ttl: float = 3600,  # 内存缓存TTL (秒)
        file_ttl: float = 86400,   # 文件缓存TTL (秒)
        max_memory_entries: int = 10000,
        enable_compression: bool = True,
        enable_file_cache: bool = True
    ):
        self.cache_dir = Path(cache_dir)
        self.memory_ttl = memory_ttl
        self.file_ttl = file_ttl
        self.max_memory_entries = max_memory_entries
        self.enable_compression = enable_compression
        self.enable_file_cache = enable_file_cache

        # 缓存文件路径
        self.brain_api_cache_file = self.cache_dir / "brain_api_cache.pkl"
        self.clickhouse_cache_file = self.cache_dir / "clickhouse_cache.pkl"

        # 内存缓存（个体和批量）
        self._memory_cache: Dict[str, CacheEntry] = {}
        self._batch_memory_cache: Dict[str, BatchCacheEntry] = {}
        self._memory_lock = RLock()

        # 文件锁
        self._brain_api_lock = RLock()
        self._clickhouse_lock = RLock()

        # 统计信息
        self.stats = CacheStats()

        # 日志
        self.logger = logging.getLogger(__name__)

        # 确保缓存目录存在
        if self.enable_file_cache:
            self.cache_dir.mkdir(parents=True, exist_ok=True)
            self._initialize_cache_files()
            self.logger.info(f"缓存目录: {self.cache_dir}")
    
    def _initialize_cache_files(self):
        """初始化缓存文件"""
        for cache_file in [self.brain_api_cache_file, self.clickhouse_cache_file]:
            if not cache_file.exists():
                self._create_empty_cache_file(cache_file)

    def _create_empty_cache_file(self, file_path: Path):
        """创建空的缓存文件"""
        try:
            cache = ConsolidatedCache()
            self._save_cache_file(file_path, cache)
            self.logger.debug(f"创建空缓存文件: {file_path}")
        except Exception as e:
            self.logger.error(f"创建缓存文件失败 {file_path}: {e}")

    def _generate_key(self, key: str) -> str:
        """生成缓存键的哈希值"""
        return hashlib.md5(key.encode('utf-8')).hexdigest()

    def _generate_batch_key(self, smiles_list: List[str]) -> str:
        """生成批量缓存键"""
        # 排序SMILES列表以确保一致的键
        sorted_smiles = sorted(smiles_list)
        combined_string = "|".join(sorted_smiles)
        return hashlib.md5(combined_string.encode('utf-8')).hexdigest()

    def _get_cache_file_for_type(self, cache_type: str) -> Path:
        """根据缓存类型获取缓存文件路径"""
        if cache_type.lower() == 'brain_api':
            return self.brain_api_cache_file
        elif cache_type.lower() == 'clickhouse':
            return self.clickhouse_cache_file
        else:
            # 默认使用brain_api缓存文件
            return self.brain_api_cache_file

    def _get_lock_for_type(self, cache_type: str) -> RLock:
        """根据缓存类型获取对应的锁"""
        if cache_type.lower() == 'brain_api':
            return self._brain_api_lock
        elif cache_type.lower() == 'clickhouse':
            return self._clickhouse_lock
        else:
            return self._brain_api_lock

    @contextmanager
    def _file_lock(self, cache_type: str):
        """文件锁上下文管理器"""
        lock = self._get_lock_for_type(cache_type)
        lock.acquire()
        try:
            yield
        finally:
            lock.release()
    
    def _save_cache_file(self, file_path: Path, cache: ConsolidatedCache):
        """保存合并缓存文件"""
        try:
            # 使用临时文件确保原子写入
            temp_file = file_path.with_suffix('.tmp')

            if self.enable_compression:
                with gzip.open(temp_file, 'wb') as f:
                    pickle.dump(cache, f)
            else:
                with open(temp_file, 'wb') as f:
                    pickle.dump(cache, f)

            # 原子性地替换原文件
            temp_file.replace(file_path)

        except Exception as e:
            self.logger.error(f"保存缓存文件失败 {file_path}: {e}")
            # 清理临时文件
            if temp_file.exists():
                temp_file.unlink(missing_ok=True)
            raise

    def _load_cache_file(self, file_path: Path) -> Optional[ConsolidatedCache]:
        """加载合并缓存文件"""
        try:
            if not file_path.exists():
                return None

            if self.enable_compression:
                with gzip.open(file_path, 'rb') as f:
                    cache = pickle.load(f)
            else:
                with open(file_path, 'rb') as f:
                    cache = pickle.load(f)

            return cache
        except Exception as e:
            self.logger.warning(f"加载缓存文件失败 {file_path}: {e}")
            return None
    
    def cache_batch(self, batch_key: str, smiles_list: List[str], results: Dict[str, Any], cache_type: str = 'brain_api') -> bool:
        """缓存整个API响应批次"""
        if not self.enable_file_cache:
            return False

        try:
            # 创建批量缓存条目
            batch_entry = BatchCacheEntry(
                smiles_list=smiles_list,
                results=results,
                timestamp=time.time()
            )

            # 添加到内存缓存
            with self._memory_lock:
                self._batch_memory_cache[batch_key] = batch_entry
                # 同时缓存个体SMILES（向后兼容）
                for smiles, result in results.items():
                    individual_key = f"{cache_type}:{smiles}"
                    self._memory_cache[individual_key] = CacheEntry(
                        data=result,
                        timestamp=time.time()
                    )

            # 保存到文件缓存
            cache_file = self._get_cache_file_for_type(cache_type)

            with self._file_lock(cache_type):
                # 加载现有缓存
                cache = self._load_cache_file(cache_file)
                if cache is None:
                    cache = ConsolidatedCache()

                # 添加批量条目
                cache.batch_entries[batch_key] = batch_entry

                # 添加个体条目（向后兼容）
                for smiles, result in results.items():
                    individual_key = f"{cache_type}:{smiles}"
                    cache.individual_entries[individual_key] = CacheEntry(
                        data=result,
                        timestamp=time.time()
                    )

                # 清理过期条目
                expired_count = cache.cleanup_expired(self.file_ttl)
                if expired_count > 0:
                    self.stats.evictions += expired_count

                # 保存缓存文件
                self._save_cache_file(cache_file, cache)

            self.logger.debug(f"批量缓存成功: {batch_key}, {len(smiles_list)} SMILES")
            return True

        except Exception as e:
            self.logger.error(f"批量缓存失败 {batch_key}: {e}")
            return False
    
    def get_cached_batch(self, batch_key: str, smiles_list: List[str], cache_type: str = 'brain_api') -> Tuple[Dict[str, Any], List[str]]:
        """
        获取批量缓存结果
        返回: (缓存的结果字典, 缺失的SMILES列表)
        """
        cached_results = {}
        missing_smiles = []

        # 首先检查内存中的批量缓存
        with self._memory_lock:
            if batch_key in self._batch_memory_cache:
                batch_entry = self._batch_memory_cache[batch_key]
                if not batch_entry.is_expired(self.memory_ttl):
                    batch_entry.touch()
                    cached_results = batch_entry.get_results_for_smiles(smiles_list)
                    missing_smiles = [s for s in smiles_list if s not in cached_results]

                    if len(cached_results) == len(smiles_list):
                        self.stats.hits += 1
                    elif len(cached_results) > 0:
                        self.stats.partial_hits += 1
                    else:
                        self.stats.misses += 1

                    return cached_results, missing_smiles
                else:
                    # 批量缓存过期，删除
                    del self._batch_memory_cache[batch_key]
                    self.stats.evictions += 1

        # 检查文件缓存
        if self.enable_file_cache:
            cache_file = self._get_cache_file_for_type(cache_type)

            with self._file_lock(cache_type):
                cache = self._load_cache_file(cache_file)
                if cache and batch_key in cache.batch_entries:
                    batch_entry = cache.batch_entries[batch_key]
                    if not batch_entry.is_expired(self.file_ttl):
                        batch_entry.touch()
                        cached_results = batch_entry.get_results_for_smiles(smiles_list)
                        missing_smiles = [s for s in smiles_list if s not in cached_results]

                        # 将批量条目加载到内存缓存
                        with self._memory_lock:
                            self._batch_memory_cache[batch_key] = batch_entry

                        if len(cached_results) == len(smiles_list):
                            self.stats.hits += 1
                        elif len(cached_results) > 0:
                            self.stats.partial_hits += 1
                        else:
                            self.stats.misses += 1

                        return cached_results, missing_smiles

        # 如果没有批量缓存，尝试个体SMILES缓存
        for smiles in smiles_list:
            individual_result = self.get(f"{cache_type}:{smiles}")
            if individual_result is not None:
                cached_results[smiles] = individual_result
            else:
                missing_smiles.append(smiles)

        if len(cached_results) == len(smiles_list):
            self.stats.hits += 1
        elif len(cached_results) > 0:
            self.stats.partial_hits += 1
        else:
            self.stats.misses += 1

        return cached_results, missing_smiles

    def get_missing_smiles(self, smiles_list: List[str], cache_type: str = 'brain_api') -> List[str]:
        """获取未缓存的SMILES列表"""
        missing_smiles = []

        for smiles in smiles_list:
            if self.get(f"{cache_type}:{smiles}") is None:
                missing_smiles.append(smiles)

        return missing_smiles
    
    def _cleanup_memory_cache(self):
        """清理内存缓存中的过期条目"""
        with self._memory_lock:
            # 清理个体缓存
            expired_keys = [
                key for key, entry in self._memory_cache.items()
                if entry.is_expired(self.memory_ttl)
            ]

            for key in expired_keys:
                del self._memory_cache[key]
                self.stats.evictions += 1

            # 清理批量缓存
            expired_batch_keys = [
                key for key, entry in self._batch_memory_cache.items()
                if entry.is_expired(self.memory_ttl)
            ]

            for key in expired_batch_keys:
                del self._batch_memory_cache[key]
                self.stats.evictions += 1

            # 如果超过最大条目数，删除最少使用的条目
            total_entries = len(self._memory_cache) + len(self._batch_memory_cache)
            if total_entries > self.max_memory_entries:
                # 合并所有条目并按访问时间排序
                all_items = []
                for key, entry in self._memory_cache.items():
                    all_items.append(('individual', key, entry.last_access))
                for key, entry in self._batch_memory_cache.items():
                    all_items.append(('batch', key, entry.last_access))

                all_items.sort(key=lambda x: x[2])  # 按访问时间排序

                excess_count = total_entries - self.max_memory_entries
                for item_type, key, _ in all_items[:excess_count]:
                    if item_type == 'individual':
                        del self._memory_cache[key]
                    else:
                        del self._batch_memory_cache[key]
                    self.stats.evictions += 1

    def get(self, key: str) -> Optional[Any]:
        """获取缓存数据（向后兼容）"""
        # 首先检查内存缓存
        with self._memory_lock:
            if key in self._memory_cache:
                entry = self._memory_cache[key]
                if not entry.is_expired(self.memory_ttl):
                    entry.touch()
                    self.stats.hits += 1
                    return entry.data
                else:
                    del self._memory_cache[key]
                    self.stats.evictions += 1

        # 检查文件缓存
        cache_type = 'brain_api' if 'brain_api' in key else 'clickhouse'
        cache_file = self._get_cache_file_for_type(cache_type)

        if self.enable_file_cache:
            with self._file_lock(cache_type):
                cache = self._load_cache_file(cache_file)
                if cache and key in cache.individual_entries:
                    entry = cache.individual_entries[key]
                    if not entry.is_expired(self.file_ttl):
                        entry.touch()
                        # 将文件缓存数据加载到内存缓存
                        with self._memory_lock:
                            self._memory_cache[key] = entry
                        self.stats.hits += 1
                        return entry.data
                    else:
                        # 过期条目，从缓存中移除
                        del cache.individual_entries[key]
                        self._save_cache_file(cache_file, cache)
                        self.stats.evictions += 1

        self.stats.misses += 1
        return None
    
    def set(self, key: str, data: Any) -> bool:
        """设置缓存数据（向后兼容）"""
        try:
            # 清理过期的内存缓存
            self._cleanup_memory_cache()

            # 保存到内存缓存
            cache_entry = CacheEntry(data=data, timestamp=time.time())
            with self._memory_lock:
                self._memory_cache[key] = cache_entry

            # 保存到文件缓存
            if self.enable_file_cache:
                cache_type = 'brain_api' if 'brain_api' in key else 'clickhouse'
                cache_file = self._get_cache_file_for_type(cache_type)

                with self._file_lock(cache_type):
                    # 加载现有缓存
                    cache = self._load_cache_file(cache_file)
                    if cache is None:
                        cache = ConsolidatedCache()

                    # 添加个体条目
                    cache.individual_entries[key] = cache_entry

                    # 清理过期条目
                    expired_count = cache.cleanup_expired(self.file_ttl)
                    if expired_count > 0:
                        self.stats.evictions += expired_count

                    # 保存缓存文件
                    self._save_cache_file(cache_file, cache)

            return True
        except Exception as e:
            self.logger.error(f"设置缓存失败 {key}: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """删除缓存数据"""
        deleted = False

        # 从内存缓存删除
        with self._memory_lock:
            if key in self._memory_cache:
                del self._memory_cache[key]
                deleted = True

        # 从文件缓存删除
        if self.enable_file_cache:
            cache_type = 'brain_api' if 'brain_api' in key else 'clickhouse'
            cache_file = self._get_cache_file_for_type(cache_type)

            with self._file_lock(cache_type):
                cache = self._load_cache_file(cache_file)
                if cache and key in cache.individual_entries:
                    del cache.individual_entries[key]
                    self._save_cache_file(cache_file, cache)
                    deleted = True

        return deleted

    def delete_batch(self, batch_key: str, cache_type: str = 'brain_api') -> bool:
        """删除批量缓存数据"""
        deleted = False

        # 从内存缓存删除
        with self._memory_lock:
            if batch_key in self._batch_memory_cache:
                del self._batch_memory_cache[batch_key]
                deleted = True

        # 从文件缓存删除
        if self.enable_file_cache:
            cache_file = self._get_cache_file_for_type(cache_type)

            with self._file_lock(cache_type):
                cache = self._load_cache_file(cache_file)
                if cache and batch_key in cache.batch_entries:
                    del cache.batch_entries[batch_key]
                    self._save_cache_file(cache_file, cache)
                    deleted = True

        return deleted
    
    def clear(self):
        """清空所有缓存"""
        # 清空内存缓存
        with self._memory_lock:
            self._memory_cache.clear()
            self._batch_memory_cache.clear()

        # 清空文件缓存
        if self.enable_file_cache:
            for cache_file in [self.brain_api_cache_file, self.clickhouse_cache_file]:
                if cache_file.exists():
                    cache_file.unlink(missing_ok=True)
                # 重新创建空文件
                self._create_empty_cache_file(cache_file)

        # 重置统计
        self.stats.reset()
        self.logger.info("缓存已清空")
    
    def get_stats(self) -> CacheStats:
        """获取缓存统计信息"""
        with self._memory_lock:
            self.stats.memory_entries = len(self._memory_cache)
            self.stats.batch_entries = len(self._batch_memory_cache)

        if self.enable_file_cache:
            total_file_entries = 0
            for cache_file in [self.brain_api_cache_file, self.clickhouse_cache_file]:
                if cache_file.exists():
                    cache = self._load_cache_file(cache_file)
                    if cache:
                        total_file_entries += len(cache.individual_entries) + len(cache.batch_entries)
            self.stats.file_entries = total_file_entries

        return self.stats

    def cleanup_expired(self):
        """清理所有过期的缓存条目"""
        # 清理内存缓存
        self._cleanup_memory_cache()

        # 清理文件缓存
        if self.enable_file_cache:
            for cache_type in ['brain_api', 'clickhouse']:
                cache_file = self._get_cache_file_for_type(cache_type)

                with self._file_lock(cache_type):
                    try:
                        cache = self._load_cache_file(cache_file)
                        if cache:
                            expired_count = cache.cleanup_expired(self.file_ttl)
                            if expired_count > 0:
                                self._save_cache_file(cache_file, cache)
                                self.stats.evictions += expired_count
                    except Exception as e:
                        self.logger.warning(f"清理缓存文件失败 {cache_file}: {e}")

        self.logger.info("过期缓存清理完成")


# 向后兼容的别名
CacheManager = BatchCacheManager
